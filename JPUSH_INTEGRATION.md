# 极光推送集成说明

## 概述

本项目已成功集成极光推送（JPush），支持 Android 和 iOS 平台的推送通知功能。

## 配置信息

- **App Key**: `e4b1e2e32964eff17b239b67`
- **Master Secret**: `4e16a5e62b65ba99177ffff2`

## 已完成的集成工作

### 1. Flutter 依赖配置

在 `pubspec.yaml` 中已添加：
```yaml
jpush_flutter:
  git:
    url: https://github.com/jpush/jpush-flutter-plugin.git
    ref: dev-3.x
```

### 2. Android 配置

#### AndroidManifest.xml 配置
- 添加了极光推送的 meta-data 配置
- 配置了推送相关的权限
- 添加了自定义消息接收器

#### 创建的文件
- `JPushReceiver.kt`: 自定义推送消息接收器
- 更新了 `MainActivity.kt` 以初始化极光推送

### 3. iOS 配置

#### Info.plist 配置
- 添加了极光推送的 App Key 和 Channel 配置
- 配置了后台模式支持远程通知

#### AppDelegate.swift 更新
- 添加了极光推送初始化代码
- 实现了通知权限申请
- 添加了通知接收和点击处理

### 4. Flutter 服务类

#### JPushService (`lib/services/jpush_service.dart`)
提供完整的推送服务功能：
- 推送初始化
- 消息监听
- 别名和标签管理
- 通知权限申请
- 推送开关控制

#### PushTestHelper (`lib/utils/push_test_helper.dart`)
提供推送测试功能：
- 发送推送给指定 Registration ID
- 发送推送给指定别名
- 发送推送给指定标签
- 发送广播推送

### 5. 示例页面

#### PushSettingsPage (`lib/pages/push_settings_page.dart`)
推送设置管理页面，包含：
- Registration ID 显示和复制
- 别名设置和删除
- 标签添加和管理
- 通知清除

#### JPushExample (`lib/examples/jpush_example.dart`)
推送功能演示页面，展示：
- 推送初始化状态
- 基础操作示例
- 测试推送发送

## 使用方法

### 1. 初始化推送服务

在 `main.dart` 中已自动初始化：
```dart
await JPushService().initialize();
```

### 2. 设置推送监听

```dart
final jpushService = JPushService();

jpushService.onReceiveNotification = (message) {
  // 处理接收到的通知
  print('收到通知: $message');
};

jpushService.onOpenNotification = (message) {
  // 处理点击通知
  print('点击通知: $message');
};

jpushService.onReceiveMessage = (message) {
  // 处理自定义消息
  print('收到自定义消息: $message');
};
```

### 3. 设置用户别名和标签

```dart
// 设置别名
await jpushService.setAlias('user_123');

// 设置标签
await jpushService.setTags(['vip', 'android_user']);
```

### 4. 发送测试推送

```dart
// 发送推送给指定 Registration ID
final success = await PushTestHelper.sendPushToRegistrationId(
  registrationId: registrationId,
  title: '测试推送',
  content: '这是一条测试推送消息',
  extras: {'type': 'test'},
);
```

## 推送消息格式

### 通知消息
```json
{
  "platform": ["android", "ios"],
  "audience": {
    "registration_id": ["registration_id"]
  },
  "notification": {
    "alert": "通知内容",
    "android": {
      "title": "通知标题",
      "alert": "通知内容",
      "extras": {
        "type": "device",
        "deviceId": "device_001"
      }
    },
    "ios": {
      "alert": "通知内容",
      "badge": 1,
      "sound": "default",
      "extras": {
        "type": "device",
        "deviceId": "device_001"
      }
    }
  }
}
```

### 自定义消息
```json
{
  "platform": ["android", "ios"],
  "audience": {
    "registration_id": ["registration_id"]
  },
  "message": {
    "msg_content": "自定义消息内容",
    "title": "消息标题",
    "extras": {
      "type": "custom",
      "data": "custom_data"
    }
  }
}
```

## 推送场景示例

### 1. 设备状态通知
```dart
final extras = PushTestHelper.createDeviceExtras(
  deviceId: 'device_001',
  deviceName: '智能灯泡',
);

await PushTestHelper.sendPushToRegistrationId(
  registrationId: registrationId,
  title: '设备通知',
  content: '您的智能灯泡状态发生变化',
  extras: extras,
);
```

### 2. 系统消息推送
```dart
await PushTestHelper.sendPushToAlias(
  alias: 'user_123',
  title: '系统消息',
  content: '您有新的系统消息',
  extras: PushTestHelper.createHomeExtras(),
);
```

## 注意事项

1. **开发环境配置**: 当前配置为开发环境，生产环境需要修改：
   - Android: `android:value="false"` 改为 `android:value="true"`
   - iOS: `apsForProduction: false` 改为 `apsForProduction: true`

2. **权限申请**: iOS 需要用户手动授权通知权限

3. **证书配置**: iOS 生产环境需要配置正确的推送证书

4. **网络要求**: 推送功能需要网络连接

5. **测试建议**: 
   - 使用真机测试推送功能
   - 确保应用在前台和后台都能正常接收推送
   - 测试不同类型的推送消息

## 故障排除

1. **推送收不到**: 检查网络连接和 Registration ID 是否正确
2. **iOS 推送失败**: 检查推送证书配置和权限授权
3. **Android 推送失败**: 检查应用签名和包名配置
4. **自定义消息处理**: 确保正确实现消息监听回调

## 相关文件

- `lib/services/jpush_service.dart` - 推送服务类
- `lib/utils/push_test_helper.dart` - 推送测试工具
- `lib/pages/push_settings_page.dart` - 推送设置页面
- `lib/examples/jpush_example.dart` - 使用示例
- `android/app/src/main/kotlin/.../JPushReceiver.kt` - Android 消息接收器
- `ios/Runner/AppDelegate.swift` - iOS 推送配置

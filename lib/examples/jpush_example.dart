import 'package:flutter/material.dart';
import '../services/jpush_service.dart';
import '../utils/push_test_helper.dart';
import '../pages/push_settings_page.dart';

/// 极光推送使用示例
class JPushExample extends StatefulWidget {
  const JPushExample({Key? key}) : super(key: key);

  @override
  State<JPushExample> createState() => _JPushExampleState();
}

class _JPushExampleState extends State<JPushExample> {
  final JPushService _jpushService = JPushService();
  String _status = '未初始化';
  String? _registrationId;

  @override
  void initState() {
    super.initState();
    _initPush();
  }

  /// 初始化推送
  void _initPush() async {
    setState(() {
      _status = '初始化中...';
    });

    try {
      await _jpushService.initialize();
      _registrationId = _jpushService.registrationId;
      
      // 设置推送监听器
      _jpushService.onReceiveNotification = (message) {
        _showDialog('收到通知', message.toString());
      };

      _jpushService.onOpenNotification = (message) {
        _showDialog('点击通知', message.toString());
      };

      _jpushService.onReceiveMessage = (message) {
        _showDialog('收到自定义消息', message.toString());
      };

      setState(() {
        _status = '初始化成功';
      });
    } catch (e) {
      setState(() {
        _status = '初始化失败: $e';
      });
    }
  }

  /// 显示对话框
  void _showDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  /// 设置用户别名
  void _setUserAlias() async {
    final success = await _jpushService.setAlias('user_123');
    _showMessage(success ? '设置别名成功' : '设置别名失败');
  }

  /// 设置用户标签
  void _setUserTags() async {
    final success = await _jpushService.setTags(['vip', 'android_user']);
    _showMessage(success ? '设置标签成功' : '设置标签失败');
  }

  /// 发送测试推送
  void _sendTestPush() async {
    if (_registrationId == null) {
      _showMessage('Registration ID 为空，无法发送推送');
      return;
    }

    final success = await PushTestHelper.sendPushToRegistrationId(
      registrationId: _registrationId!,
      title: '测试推送',
      content: '这是一条测试推送消息',
      extras: {
        'type': 'test',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );

    _showMessage(success ? '测试推送发送成功' : '测试推送发送失败');
  }

  /// 发送设备相关推送
  void _sendDevicePush() async {
    if (_registrationId == null) {
      _showMessage('Registration ID 为空，无法发送推送');
      return;
    }

    final extras = PushTestHelper.createDeviceExtras(
      deviceId: 'device_001',
      deviceName: '智能灯泡',
    );

    final success = await PushTestHelper.sendPushToRegistrationId(
      registrationId: _registrationId!,
      title: '设备通知',
      content: '您的智能灯泡状态发生变化',
      extras: extras,
    );

    _showMessage(success ? '设备推送发送成功' : '设备推送发送失败');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('极光推送示例'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PushSettingsPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '推送状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('状态: $_status'),
                    const SizedBox(height: 4),
                    Text('Registration ID: ${_registrationId ?? '未获取'}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 基础操作
            Text(
              '基础操作',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _setUserAlias,
                child: const Text('设置用户别名'),
              ),
            ),
            
            const SizedBox(height: 8),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _setUserTags,
                child: const Text('设置用户标签'),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 测试推送
            Text(
              '测试推送',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _sendTestPush,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('发送测试推送'),
              ),
            ),
            
            const SizedBox(height: 8),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _sendDevicePush,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('发送设备推送'),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 说明文本
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    '使用说明:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('1. 首先确保推送初始化成功'),
                  Text('2. 设置用户别名和标签用于精准推送'),
                  Text('3. 点击测试推送按钮发送推送消息'),
                  Text('4. 在设置页面可以管理更多推送选项'),
                  Text('5. 推送消息会在通知栏显示'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

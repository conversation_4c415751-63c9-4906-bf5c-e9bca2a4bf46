import 'package:flutter/material.dart';
import '../services/jpush_navigation_service.dart';
import '../utils/push_test_helper.dart';

/// 极光推送跳转示例 - 统一在 Flutter 端处理跳转
class JPushNavigationExample extends StatefulWidget {
  const JPushNavigationExample({Key? key}) : super(key: key);

  @override
  State<JPushNavigationExample> createState() => _JPushNavigationExampleState();
}

class _JPushNavigationExampleState extends State<JPushNavigationExample> {
  final JPushService _jpushService = JPushService();
  String _status = '未初始化';
  String? _registrationId;
  String _lastNavigation = '无';

  @override
  void initState() {
    super.initState();
    _initPush();
  }

  /// 初始化推送
  void _initPush() async {
    setState(() {
      _status = '初始化中...';
    });

    try {
      // 设置跳转回调 - 统一处理所有跳转逻辑
      _jpushService.onNavigate = (type, data) {
        _handleNavigation(type, data);
      };

      await _jpushService.initialize();
      _registrationId = _jpushService.registrationId;

      setState(() {
        _status = '初始化成功';
      });
    } catch (e) {
      setState(() {
        _status = '初始化失败: $e';
      });
    }
  }

  /// 统一处理跳转逻辑 - 只在这里处理跳转
  void _handleNavigation(String type, Map<String, dynamic> data) {
    setState(() {
      _lastNavigation = '$type: $data';
    });

    switch (type) {
      case 'device':
        final deviceId = data['deviceId'] as String?;
        final deviceName = data['deviceName'] as String?;
        _navigateToDevice(deviceId, deviceName);
        break;
      case 'home':
        _navigateToHome();
        break;
      case 'settings':
        _navigateToSettings();
        break;
      default:
        _showMessage('未知推送类型: $type');
    }
  }

  /// 跳转到设备详情页
  void _navigateToDevice(String? deviceId, String? deviceName) {
    _showDialog(
      '设备跳转',
      '跳转到设备详情页\n设备ID: $deviceId\n设备名称: $deviceName',
    );

    // TODO: 在这里添加实际的页面跳转逻辑
    // Navigator.pushNamed(context, '/device', arguments: {'deviceId': deviceId});
  }

  /// 跳转到首页
  void _navigateToHome() {
    _showDialog('首页跳转', '跳转到首页');

    // TODO: 在这里添加实际的页面跳转逻辑
    // Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
  }

  /// 跳转到设置页
  void _navigateToSettings() {
    _showDialog('设置跳转', '跳转到设置页');

    // TODO: 在这里添加实际的页面跳转逻辑
    // Navigator.pushNamed(context, '/settings');
  }

  /// 显示对话框
  void _showDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  /// 发送设备推送测试
  void _sendDevicePush() async {
    if (_registrationId == null) {
      _showMessage('Registration ID 为空，无法发送推送');
      return;
    }

    final extras = PushTestHelper.createDeviceExtras(
      deviceId: 'device_001',
      deviceName: '智能灯泡',
    );

    final success = await PushTestHelper.sendPushToRegistrationId(
      registrationId: _registrationId!,
      title: '设备通知',
      content: '您的智能灯泡状态发生变化',
      extras: extras,
    );

    _showMessage(success ? '设备推送发送成功' : '设备推送发送失败');
  }

  /// 发送首页推送测试
  void _sendHomePush() async {
    if (_registrationId == null) {
      _showMessage('Registration ID 为空，无法发送推送');
      return;
    }

    final extras = PushTestHelper.createHomeExtras();

    final success = await PushTestHelper.sendPushToRegistrationId(
      registrationId: _registrationId!,
      title: '系统通知',
      content: '您有新的系统消息',
      extras: extras,
    );

    _showMessage(success ? '首页推送发送成功' : '首页推送发送失败');
  }

  /// 发送设置页推送测试
  void _sendSettingsPush() async {
    if (_registrationId == null) {
      _showMessage('Registration ID 为空，无法发送推送');
      return;
    }

    final extras = {
      'type': 'settings',
      'section': 'account',
    };

    final success = await PushTestHelper.sendPushToRegistrationId(
      registrationId: _registrationId!,
      title: '账户通知',
      content: '请更新您的账户信息',
      extras: extras,
    );

    _showMessage(success ? '设置推送发送成功' : '设置推送发送失败');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('极光推送跳转示例'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '推送状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('状态: $_status'),
                    const SizedBox(height: 4),
                    Text('Registration ID: ${_registrationId ?? '未获取'}'),
                    const SizedBox(height: 4),
                    Text('最后跳转: $_lastNavigation'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 测试推送按钮
            Text(
              '测试推送跳转',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _sendDevicePush,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('发送设备推送'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _sendHomePush,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('发送首页推送'),
              ),
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _sendSettingsPush,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
                child: const Text('发送设置推送'),
              ),
            ),

            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _jpushService.testNavigation(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('测试跳转功能（模拟）'),
              ),
            ),

            const SizedBox(height: 20),

            // 说明文本
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '跳转逻辑说明:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text('1. 所有跳转逻辑统一在 Flutter 端处理'),
                  Text('2. 原生端只负责传递推送数据'),
                  Text('3. 通过 onNavigate 回调统一处理跳转'),
                  Text('4. 支持应用前台、后台、关闭状态的跳转'),
                  Text('5. 可以根据推送类型执行不同的跳转逻辑'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
